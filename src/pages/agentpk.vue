<template>
  <div class="agentpk-fullscreen">
    <!-- 顶部返回按钮 -->
    <div class="top-bar">
      <button class="back-button" @click="goBack">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        返回
      </button>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <h1 class="hello-world">Hello World</h1>
      <p class="description">这是一个占满浏览器全屏的非H5页面</p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { onMounted, onUnmounted } from 'vue';

const router = useRouter();

// 返回首页
const goBack = () => {
  console.log('🔙 [agentpk] 返回首页');
  router.push({
    name: 'chat', // 返回到index.vue (chat路由)
  });
};

// 设置全屏样式
const setFullscreenStyles = () => {
  // 添加全屏样式类
  document.documentElement.classList.add('agentpk-active');
  document.body.classList.add('agentpk-active');
  const app = document.getElementById('app');
  if (app) {
    app.classList.add('agentpk-active');
  }

  console.log('🎨 [agentpk] 已应用全屏样式');
};

// 移除全屏样式
const removeFullscreenStyles = () => {
  document.documentElement.classList.remove('agentpk-active');
  document.body.classList.remove('agentpk-active');
  const app = document.getElementById('app');
  if (app) {
    app.classList.remove('agentpk-active');
  }

  console.log('🎨 [agentpk] 已移除全屏样式');
};

onMounted(() => {
  console.log('🚀 [agentpk] 全屏页面加载完成');
  setFullscreenStyles();
});

onUnmounted(() => {
  console.log('🔚 [agentpk] 页面卸载，恢复原始样式');
  removeFullscreenStyles();
});
</script>

<style lang="scss" scoped>
.agentpk-fullscreen {
  // 占满整个浏览器窗口，不受H5布局限制
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: 9999;
  overflow: hidden;
  
  // 确保不受父容器样式影响
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.top-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 60px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  z-index: 10;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);

  &:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }

  svg {
    width: 18px;
    height: 18px;
  }
}

.main-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: white;
  width: 100%;
  max-width: 800px;
  padding: 0 20px;
}

.hello-world {
  font-size: 4rem;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  margin: 0 0 20px 0;
  animation: fadeInScale 1s ease-out;
}

.description {
  font-size: 1.2rem;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  margin: 0;
  animation: fadeInUp 1s ease-out 0.3s both;
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 0.9;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .hello-world {
    font-size: 2.5rem;
  }
  
  .description {
    font-size: 1rem;
  }
  
  .top-bar {
    height: 50px;
    padding: 0 15px;
  }
  
  .back-button {
    padding: 6px 12px;
    font-size: 13px;
    
    svg {
      width: 16px;
      height: 16px;
    }
  }
}

@media (max-width: 480px) {
  .hello-world {
    font-size: 2rem;
  }
  
  .description {
    font-size: 0.9rem;
  }
}
</style>

<style>
/* 全局样式，确保页面真正占满全屏 */
body.agentpk-active {
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

html.agentpk-active {
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
}

#app.agentpk-active {
  overflow: hidden !important;
}
</style>
