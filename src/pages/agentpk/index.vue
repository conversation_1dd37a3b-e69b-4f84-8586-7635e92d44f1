<template>
  <div class="agentpk-container">
    <div class="agentpk-content">
      <h1 class="hello-world">Hello World</h1>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted } from 'vue';

// 进入全屏模式
const enterFullscreen = () => {
  const element = document.documentElement;
  if (element.requestFullscreen) {
    element.requestFullscreen().catch((err) => {
      console.warn('无法进入全屏模式:', err);
    });
  } else if ((element as any).webkitRequestFullscreen) {
    // Safari 支持
    (element as any).webkitRequestFullscreen();
  } else if ((element as any).msRequestFullscreen) {
    // IE/Edge 支持
    (element as any).msRequestFullscreen();
  }
};

// 退出全屏模式
const exitFullscreen = () => {
  if (document.exitFullscreen) {
    document.exitFullscreen().catch((err) => {
      console.warn('无法退出全屏模式:', err);
    });
  } else if ((document as any).webkitExitFullscreen) {
    // Safari 支持
    (document as any).webkitExitFullscreen();
  } else if ((document as any).msExitFullscreen) {
    // IE/Edge 支持
    (document as any).msExitFullscreen();
  }
};

// 监听ESC键退出全屏
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    exitFullscreen();
  }
};

onMounted(() => {
  console.log('🚀 [agentpk] 页面加载，尝试进入全屏模式');
  
  // 延迟一下再进入全屏，确保页面完全加载
  setTimeout(() => {
    enterFullscreen();
  }, 100);
  
  // 监听键盘事件
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  console.log('🔚 [agentpk] 页面卸载，清理事件监听');
  
  // 清理事件监听
  document.removeEventListener('keydown', handleKeydown);
  
  // 退出全屏模式
  exitFullscreen();
});
</script>

<style lang="scss" scoped>
.agentpk-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  overflow: hidden;
}

.agentpk-content {
  text-align: center;
  color: white;
}

.hello-world {
  font-size: 4rem;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  margin: 0;
  animation: fadeInScale 1s ease-out;
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .hello-world {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  .hello-world {
    font-size: 2rem;
  }
}
</style>
