import { RouteRecordRaw } from 'vue-router';
import { RouteName } from '@/constants/route-name';

const routes: Array<RouteRecordRaw> = [
  {
    path: 'chat',
    component: () => import(/* webpackChunkName: "chat" */ '@/pages/Chat/index.vue'),
    name: RouteName.CHAT,
    meta: {
      cid: 'c_smartassistant_v4jfr1ai',
    },
  },
  {
    path: 'chat/:title',
    component: () => import(/* webpackChunkName: "chat-conversation" */ '@/pages/Chat/chat.vue'),
    name: 'chat-conversation',
    meta: {
      cid: 'c_smartassistant_chat_conversation',
    },
  },
  {
    path: 'relationship-graph',
    component: () =>
      import(/* webpackChunkName: "relationship-graph" */ '@/pages/Chat/relationGraph.vue'),
    name: RouteName.RELATIONSHIP_GRAPH,
    meta: {
      cid: 'c_smartassistant_relationship_graph',
    },
  },
  {
    path: 'weather',
    component: () => import(/* webpackChunkName: "weather" */ '@/pages/weather.vue'),
    name: 'weather',
    meta: {
      cid: 'c_smartassistant_weather',
    },
  },
  {
    path: 'huida',
    component: () => import(/* webpackChunkName: "huida" */ '@/pages/huida.vue'),
    name: 'huida',
    meta: {
      cid: 'c_smartassistant_huida',
    },
  },
  {
    path: 'agentpk',
    component: () => import(/* webpackChunkName: "agentpk" */ '@/pages/agentpk/index.vue'),
    name: 'agentpk',
    meta: {
      cid: 'c_smartassistant_agentpk',
    },
  },
];

export default routes;
